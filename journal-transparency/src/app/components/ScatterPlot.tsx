/**
 * Scatter plot component for Journal Choice Transparency application.
 */

'use client';

import React, { useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import { useStore } from '../store';

const OA_TYPE_COLORS = {
  'Fully OA': '#10B981', // Green
  'Hybrid': '#F59E0B',   // Amber
  'Diamond': '#8B5CF6',  // Purple
  'Subscription': '#EF4444', // Red
  'Unknown': '#6B7280',  // Gray
};

const ScatterPlot: React.FC = () => {
  const { filteredJournals, selectedJournals, addSelectedJournal, removeSelectedJournal } = useStore();

  // Prepare data for scatter plot - sample for performance if too many points
  const scatterData = useMemo(() => {
    let journals = filteredJournals;

    // If we have too many journals, sample them for better performance
    if (journals.length > 5000) {
      // Sort by impact and take top journals plus random sample
      const sorted = [...journals].sort((a, b) => b.impact_proxy - a.impact_proxy);
      const topJournals = sorted.slice(0, 2000);
      const remaining = sorted.slice(2000);
      const randomSample = remaining
        .sort(() => 0.5 - Math.random())
        .slice(0, 3000);
      journals = [...topJournals, ...randomSample];
    }

    // Filter out journals with null APC for scatter plot
    const journalsWithAPC = journals.filter(j => j.apc_usd != null && !isNaN(j.apc_usd));

    return journalsWithAPC.map((journal) => ({
      x: journal.impact_proxy,
      y: journal.apc_usd,
      title: journal.title,
      oa_type: journal.oa_type,
      cost_efficiency: typeof journal.cost_efficiency === 'number' && journal.cost_efficiency === 999999 ? Infinity : journal.cost_efficiency,
      issn_l: journal.issn_l,
      publisher: journal.publisher,
      selected: selectedJournals.has(journal.issn_l),
    }));
  }, [filteredJournals, selectedJournals]);

  // Group data by OA type for multiple scatter series
  const dataByOAType = useMemo(() => {
    const grouped: Record<string, typeof scatterData> = {};
    scatterData.forEach((point) => {
      if (!grouped[point.oa_type]) {
        grouped[point.oa_type] = [];
      }
      grouped[point.oa_type].push(point);
    });
    return grouped;
  }, [scatterData]);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-900 mb-1">{data.title}</p>
          <p className="text-sm text-gray-600">Impact: {data.x.toFixed(2)}</p>
          <p className="text-sm text-gray-600">APC: {data.y != null ? `$${data.y.toLocaleString()}` : 'N/A'}</p>
          <p className="text-sm text-gray-600">OA Type: {data.oa_type}</p>
          <p className="text-sm text-gray-600">Publisher: {data.publisher}</p>
          <p className="text-sm text-gray-600">
            Cost Efficiency: {
              data.cost_efficiency === Infinity 
                ? '∞' 
                : data.cost_efficiency.toFixed(4)
            }
          </p>
        </div>
      );
    }
    return null;
  };

  const handlePointClick = (data: any) => {
    if (selectedJournals.has(data.issn_l)) {
      removeSelectedJournal(data.issn_l);
    } else {
      addSelectedJournal(data.issn_l);
    }
  };

  return (
    <div className="h-full">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Impact vs APC Cost
        </h3>
        <div className="text-sm text-gray-600">
          {scatterData.length} journals with APC data (of {filteredJournals.length} total)
        </div>
      </div>

      <ResponsiveContainer width="100%" height="90%">
        <ScatterChart
          margin={{
            top: 20,
            right: 20,
            bottom: 60,
            left: 60,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            type="number"
            dataKey="x"
            name="Impact Proxy"
            domain={['dataMin', 'dataMax']}
            label={{ value: 'Impact Proxy (Citations per Doc)', position: 'insideBottom', offset: -10 }}
          />
          <YAxis
            type="number"
            dataKey="y"
            name="APC (USD)"
            domain={['dataMin', 'dataMax']}
            label={{ value: 'APC (USD)', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          
          {Object.entries(dataByOAType).map(([oaType, data]) => (
            <Scatter
              key={oaType}
              name={oaType}
              data={data}
              fill={OA_TYPE_COLORS[oaType as keyof typeof OA_TYPE_COLORS] || '#6B7280'}
              onClick={handlePointClick}
              style={{ cursor: 'pointer' }}
            />
          ))}
        </ScatterChart>
      </ResponsiveContainer>

      <div className="mt-4 text-xs text-gray-500">
        Click points to select journals. Selected journals will be highlighted in the table below.
      </div>
    </div>
  );
};

export default ScatterPlot;
