/**
 * Header component for Journal Choice Transparency application.
 */

import React from 'react';

const Header: React.FC = () => {
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Journal Choice Transparency
            </h1>
            <p className="text-gray-600 mt-2">
              Find the right journal for your research based on impact, cost, and openness
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <a
              href="#about"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              About
            </a>
            <a
              href="#methodology"
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Data Sources
            </a>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
