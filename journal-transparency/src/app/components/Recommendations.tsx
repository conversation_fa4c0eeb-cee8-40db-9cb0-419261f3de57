/**
 * Recommendations component for Journal Choice Transparency application.
 */

'use client';

import React, { useMemo } from 'react';
import { useStore } from '../store';
import { Journal } from '../types';
import { Star, TrendingUp, DollarSign } from 'lucide-react';

const Recommendations: React.FC = () => {
  const { filteredJournals, addSelectedJournal } = useStore();

  const recommendations = useMemo(() => {
    if (filteredJournals.length === 0) return { highImpact: [], bestValue: [], diamond: [] };

    // High impact journals (top 10% by impact)
    const sortedByImpact = [...filteredJournals]
      .sort((a, b) => b.impact_proxy - a.impact_proxy)
      .slice(0, Math.max(5, Math.floor(filteredJournals.length * 0.1)));

    // Best value journals (high cost efficiency, excluding free journals)
    const paidJournals = filteredJournals.filter(j => j.apc_usd > 0);
    const bestValue = [...paidJournals]
      .sort((a, b) => {
        const aEff = a.cost_efficiency === 999999 ? Infinity : a.cost_efficiency;
        const bEff = b.cost_efficiency === 999999 ? Infinity : b.cost_efficiency;
        return bEff - aEff;
      })
      .slice(0, 5);

    // Diamond/Free OA journals with good impact
    const diamond = filteredJournals
      .filter(j => j.apc_usd === 0 && j.impact_proxy > 1)
      .sort((a, b) => b.impact_proxy - a.impact_proxy)
      .slice(0, 5);

    return {
      highImpact: sortedByImpact.slice(0, 5),
      bestValue,
      diamond,
    };
  }, [filteredJournals]);

  const RecommendationCard: React.FC<{
    journal: Journal;
    reason: string;
    icon: React.ComponentType<any>;
    iconColor: string;
  }> = ({ journal, reason, icon: Icon, iconColor }) => (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start gap-3">
        <Icon className={`w-5 h-5 mt-1 ${iconColor}`} />
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-gray-900 truncate">{journal.title}</h4>
          <p className="text-sm text-gray-600 mt-1">{reason}</p>
          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
            <span>Impact: {journal.impact_proxy.toFixed(2)}</span>
            <span>APC: ${journal.apc_usd.toLocaleString()}</span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              journal.oa_type === 'Fully OA' ? 'bg-green-100 text-green-800' :
              journal.oa_type === 'Hybrid' ? 'bg-yellow-100 text-yellow-800' :
              journal.oa_type === 'Diamond' ? 'bg-purple-100 text-purple-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {journal.oa_type}
            </span>
          </div>
          <button
            onClick={() => addSelectedJournal(journal.issn_l)}
            className="mt-2 text-xs text-blue-600 hover:text-blue-800 font-medium"
          >
            Add to selection
          </button>
        </div>
      </div>
    </div>
  );

  if (filteredJournals.length === 0) {
    return null;
  }

  return (
    <div className="bg-gray-50 rounded-lg p-4 mb-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Recommendations Based on Your Filters
      </h3>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* High Impact */}
        {recommendations.highImpact.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-green-600" />
              High Impact
            </h4>
            <div className="space-y-3">
              {recommendations.highImpact.map((journal) => (
                <RecommendationCard
                  key={journal.issn_l}
                  journal={journal}
                  reason={`Top ${Math.round((journal.impact_proxy / Math.max(...filteredJournals.map(j => j.impact_proxy))) * 100)}% impact in your selection`}
                  icon={TrendingUp}
                  iconColor="text-green-600"
                />
              ))}
            </div>
          </div>
        )}

        {/* Best Value */}
        {recommendations.bestValue.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <Star className="w-4 h-4 text-blue-600" />
              Best Value
            </h4>
            <div className="space-y-3">
              {recommendations.bestValue.map((journal) => (
                <RecommendationCard
                  key={journal.issn_l}
                  journal={journal}
                  reason="High impact per dollar spent"
                  icon={Star}
                  iconColor="text-blue-600"
                />
              ))}
            </div>
          </div>
        )}

        {/* Diamond/Free OA */}
        {recommendations.diamond.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <DollarSign className="w-4 h-4 text-purple-600" />
              Free & High Quality
            </h4>
            <div className="space-y-3">
              {recommendations.diamond.map((journal) => (
                <RecommendationCard
                  key={journal.issn_l}
                  journal={journal}
                  reason="No APC with good impact"
                  icon={DollarSign}
                  iconColor="text-purple-600"
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Recommendations;
