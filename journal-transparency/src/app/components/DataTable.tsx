/**
 * Data table component for Journal Choice Transparency application.
 */

'use client';

import React, { useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  flexRender,
  createColumnHelper,
  SortingState,
} from '@tanstack/react-table';
import { useStore } from '../store';
import { Journal } from '../types';
import { ExternalLink, ArrowUpDown } from 'lucide-react';
import ExportButton from './ExportButton';

const columnHelper = createColumnHelper<Journal>();

const DataTable: React.FC = () => {
  const { 
    filteredJournals, 
    selectedJournals, 
    addSelectedJournal, 
    removeSelectedJournal 
  } = useStore();

  const [sorting, setSorting] = React.useState<SortingState>([
    { id: 'impact_proxy', desc: true }
  ]);

  const columns = useMemo(
    () => [
      columnHelper.accessor('title', {
        header: 'Journal Title',
        cell: (info) => (
          <div className="flex items-center gap-2">
            <span className="font-medium text-gray-900 truncate max-w-xs">
              {info.getValue()}
            </span>
            {info.row.original.journal_url && (
              <a
                href={info.row.original.journal_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800"
              >
                <ExternalLink className="w-4 h-4" />
              </a>
            )}
          </div>
        ),
        size: 300,
      }),
      columnHelper.accessor('subject', {
        header: 'Subject',
        cell: (info) => (
          <span className="text-sm text-gray-700 truncate">
            {info.getValue()}
          </span>
        ),
        size: 150,
      }),
      columnHelper.accessor('impact_proxy', {
        header: ({ column }) => (
          <button
            className="flex items-center gap-1 hover:text-blue-600"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Impact
            <ArrowUpDown className="w-4 h-4" />
          </button>
        ),
        cell: (info) => (
          <span className="font-mono text-sm">
            {info.getValue().toFixed(2)}
          </span>
        ),
        size: 100,
      }),
      columnHelper.accessor('apc_usd', {
        header: ({ column }) => (
          <button
            className="flex items-center gap-1 hover:text-blue-600"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            APC (USD)
            <ArrowUpDown className="w-4 h-4" />
          </button>
        ),
        cell: (info) => {
          const value = info.getValue();
          return (
            <span className="font-mono text-sm">
              {value != null ? `$${value.toLocaleString()}` : 'N/A'}
            </span>
          );
        },
        size: 120,
      }),
      columnHelper.accessor('cost_efficiency', {
        header: ({ column }) => (
          <button
            className="flex items-center gap-1 hover:text-blue-600"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Cost Efficiency
            <ArrowUpDown className="w-4 h-4" />
          </button>
        ),
        cell: (info) => {
          const value = info.getValue();
          return (
            <span className="font-mono text-sm">
              {value == null ? 'N/A' : value === Infinity ? '∞' : value.toFixed(4)}
            </span>
          );
        },
        size: 130,
      }),
      columnHelper.accessor('oa_type', {
        header: 'OA Type',
        cell: (info) => {
          const oaType = info.getValue();
          const colorClass = {
            'Fully OA': 'bg-green-100 text-green-800',
            'Hybrid': 'bg-yellow-100 text-yellow-800',
            'Diamond': 'bg-purple-100 text-purple-800',
            'Subscription': 'bg-red-100 text-red-800',
            'Unknown': 'bg-gray-100 text-gray-800',
          }[oaType] || 'bg-gray-100 text-gray-800';
          
          return (
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>
              {oaType}
            </span>
          );
        },
        size: 120,
      }),
      columnHelper.accessor('publisher', {
        header: 'Publisher',
        cell: (info) => (
          <span className="text-sm text-gray-700 truncate max-w-xs">
            {info.getValue()}
          </span>
        ),
        size: 200,
      }),
    ],
    []
  );

  const table = useReactTable({
    data: filteredJournals,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  const handleRowClick = (journal: Journal) => {
    if (selectedJournals.has(journal.issn_l)) {
      removeSelectedJournal(journal.issn_l);
    } else {
      addSelectedJournal(journal.issn_l);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Journal Data
        </h3>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span>{filteredJournals.length} journals</span>
            {selectedJournals.size > 0 && (
              <span className="text-blue-600 font-medium">
                {selectedJournals.size} selected
              </span>
            )}
          </div>
          <ExportButton />
        </div>
      </div>

      <div className="flex-1 overflow-auto border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50 sticky top-0">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    style={{ width: header.getSize() }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {table.getRowModel().rows.map((row) => {
              const isSelected = selectedJournals.has(row.original.issn_l);
              return (
                <tr
                  key={row.id}
                  onClick={() => handleRowClick(row.original)}
                  className={`cursor-pointer hover:bg-gray-50 ${
                    isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                  }`}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      className="px-4 py-3 whitespace-nowrap text-sm"
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      <div className="mt-4 text-xs text-gray-500">
        Click rows to select journals. Selected journals will be highlighted in the scatter plot above.
      </div>
    </div>
  );
};

export default DataTable;
