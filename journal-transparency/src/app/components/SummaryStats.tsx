/**
 * Summary statistics component for Journal Choice Transparency application.
 */

'use client';

import React, { useMemo } from 'react';
import { useStore } from '../store';
import { BarChart3, DollarSign, Target, Zap } from 'lucide-react';

const SummaryStats: React.FC = () => {
  const { filteredJournals } = useStore();

  const stats = useMemo(() => {
    if (filteredJournals.length === 0) {
      return {
        totalJournals: 0,
        avgImpact: 0,
        avgAPC: 0,
        fullyOACount: 0,
        diamondCount: 0,
      };
    }

    const totalJournals = filteredJournals.length;
    const avgImpact = filteredJournals.reduce((sum, j) => sum + j.impact_proxy, 0) / totalJournals;
    const avgAPC = filteredJournals.reduce((sum, j) => sum + j.apc_usd, 0) / totalJournals;
    const fullyOACount = filteredJournals.filter(j => j.oa_type === 'Fully OA').length;
    const diamondCount = filteredJournals.filter(j => j.oa_type === 'Diamond').length;

    return {
      totalJournals,
      avgImpact,
      avgAPC,
      fullyOACount,
      diamondCount,
    };
  }, [filteredJournals]);

  const statCards = [
    {
      title: 'Total Journals',
      value: stats.totalJournals.toLocaleString(),
      icon: BarChart3,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Avg Impact',
      value: stats.avgImpact.toFixed(2),
      icon: Target,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Avg APC',
      value: `$${Math.round(stats.avgAPC).toLocaleString()}`,
      icon: DollarSign,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Fully Open Access',
      value: `${stats.fullyOACount} (${((stats.fullyOACount / stats.totalJournals) * 100).toFixed(1)}%)`,
      icon: Zap,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <div
            key={index}
            className={`${stat.bgColor} rounded-lg p-4 border border-gray-200`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
              <Icon className={`w-8 h-8 ${stat.color}`} />
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default SummaryStats;
