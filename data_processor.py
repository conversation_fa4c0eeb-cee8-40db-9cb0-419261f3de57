#!/usr/bin/env python3
"""
Data processing script for Journal Choice Transparency project.
Processes and merges data from multiple sources into a unified dataset.
"""

import pandas as pd
import numpy as np
import re
from pathlib import Path
import json
from datetime import datetime

def clean_mdpi_data():
    """
    Clean and process MDPI APC data.
    
    Returns:
        pd.DataFrame: Cleaned MDPI data with standardized columns
    """
    print("Processing MDPI data...")
    df = pd.read_csv('source_data/MDPI_APC.csv')
    
    # Remove empty rows
    df = df.dropna()
    
    # Clean column names
    df.columns = ['title', 'apc_chf']
    
    # Extract numeric APC values and convert CHF to USD (approximate rate: 1 CHF = 1.1 USD)
    df['apc_usd'] = df['apc_chf'].str.extract(r'(\d+)').astype(float) * 1.1
    
    # Add metadata
    df['publisher'] = 'MDPI'
    df['oa_type'] = 'Fully OA'
    df['source'] = 'MDPI_APC'
    
    # Clean title
    df['title'] = df['title'].str.strip()
    
    return df[['title', 'apc_usd', 'publisher', 'oa_type', 'source']]

def clean_scimago_data():
    """
    Clean and process SciMago Journal Ranking data.
    
    Returns:
        pd.DataFrame: Cleaned SciMago data with impact metrics
    """
    print("Processing SciMago data...")
    df = pd.read_csv('source_data/scimagojr 2024.csv', sep=';')
    
    # Clean column names
    df.columns = [col.strip() for col in df.columns]
    
    # Extract relevant columns
    columns_map = {
        'Title': 'title',
        'Issn': 'issn',
        'SJR': 'sjr',
        'H index': 'h_index',
        'Citations / Doc. (2years)': 'citations_per_doc_2yr',
        'Publisher': 'publisher',
        'Categories': 'categories',
        'Areas': 'areas'
    }
    
    df_clean = df[list(columns_map.keys())].copy()
    df_clean.columns = list(columns_map.values())
    
    # Clean numeric columns
    df_clean['sjr'] = pd.to_numeric(df_clean['sjr'].str.replace(',', '.'), errors='coerce')
    df_clean['citations_per_doc_2yr'] = pd.to_numeric(
        df_clean['citations_per_doc_2yr'].str.replace(',', '.'), errors='coerce'
    )
    
    # Use citations per doc as impact proxy
    df_clean['impact_proxy'] = df_clean['citations_per_doc_2yr']
    
    # Extract primary subject from categories
    df_clean['subject'] = df_clean['categories'].str.split(';').str[0].str.strip()
    df_clean['subject'] = df_clean['subject'].str.replace(r'\s*\([^)]*\)', '', regex=True)
    
    # Clean title
    df_clean['title'] = df_clean['title'].str.strip().str.replace('"', '')
    
    df_clean['source'] = 'SciMago'
    
    return df_clean[['title', 'issn', 'impact_proxy', 'publisher', 'subject', 'source']]

def parse_wiley_excel(filepath, sheet_name=None):
    """
    Parse Wiley Excel files with proper header detection.
    
    Args:
        filepath: Path to Excel file
        sheet_name: Sheet name (optional)
    
    Returns:
        pd.DataFrame: Parsed Wiley data
    """
    print(f"Processing {filepath}...")
    
    try:
        # Read the file and find the header row
        df_raw = pd.read_excel(filepath, sheet_name=sheet_name, header=None)
        
        # Find the row with journal data (look for "Journal" or "ISSN" in first column)
        header_row = None
        for i, row in df_raw.iterrows():
            if any(str(cell).lower().find('journal') != -1 or 
                   str(cell).lower().find('issn') != -1 for cell in row if pd.notna(cell)):
                header_row = i
                break
        
        if header_row is None:
            # Try to find data rows with ISSN pattern
            for i, row in df_raw.iterrows():
                if any(re.match(r'\d{4}-\d{4}', str(cell)) for cell in row if pd.notna(cell)):
                    header_row = max(0, i - 1)
                    break
        
        if header_row is not None:
            # Re-read with proper header
            df = pd.read_excel(filepath, sheet_name=sheet_name, header=header_row)
            # Remove rows before the header
            df = df.iloc[1:].reset_index(drop=True)
        else:
            df = df_raw
        
        return df
    
    except Exception as e:
        print(f"Error parsing {filepath}: {e}")
        return pd.DataFrame()

def process_wiley_data():
    """
    Process both Wiley Excel files.
    
    Returns:
        pd.DataFrame: Combined Wiley data
    """
    # Process Open Access file
    df_oa = parse_wiley_excel('source_data/Wiley-Journal-APCs-Open-Access.xlsx')
    if not df_oa.empty:
        df_oa['oa_type'] = 'Fully OA'
    
    # Process OnlineOpen (Hybrid) file
    df_hybrid = parse_wiley_excel('source_data/Wiley-Journal-APCs-OnlineOpen.xlsx')
    if not df_hybrid.empty:
        df_hybrid['oa_type'] = 'Hybrid'
    
    # Combine dataframes
    wiley_dfs = [df for df in [df_oa, df_hybrid] if not df.empty]
    if wiley_dfs:
        df_combined = pd.concat(wiley_dfs, ignore_index=True)
        df_combined['publisher'] = 'Wiley'
        df_combined['source'] = 'Wiley'
        return df_combined
    else:
        return pd.DataFrame()

def create_unified_dataset():
    """
    Create unified dataset from all sources.
    
    Returns:
        pd.DataFrame: Unified journal dataset
    """
    print("Creating unified dataset...")
    
    # Process each data source
    mdpi_df = clean_mdpi_data()
    scimago_df = clean_scimago_data()
    wiley_df = process_wiley_data()
    
    print(f"MDPI records: {len(mdpi_df)}")
    print(f"SciMago records: {len(scimago_df)}")
    print(f"Wiley records: {len(wiley_df)}")
    
    # Start with SciMago as the base (has impact metrics)
    base_df = scimago_df.copy()
    
    # Add APC data from MDPI
    mdpi_merge = mdpi_df[['title', 'apc_usd', 'oa_type']].copy()
    base_df = base_df.merge(mdpi_merge, on='title', how='left', suffixes=('', '_mdpi'))
    
    # Fill missing values and create final columns
    base_df['apc_usd'] = base_df['apc_usd'].fillna(0)
    base_df['oa_type'] = base_df['oa_type'].fillna('Unknown')
    
    # Calculate cost efficiency
    base_df['cost_efficiency'] = base_df.apply(
        lambda row: float('inf') if row['apc_usd'] == 0 
        else row['impact_proxy'] / row['apc_usd'] if pd.notna(row['impact_proxy']) and row['apc_usd'] > 0
        else 0, axis=1
    )
    
    # Add metadata
    base_df['last_verified'] = datetime.now().strftime('%Y-%m-%d')
    base_df['license'] = 'Unknown'
    base_df['waiver'] = False
    base_df['journal_url'] = ''
    
    # Create ISSN-L (use first ISSN if multiple)
    base_df['issn_l'] = base_df['issn'].str.split(',').str[0].str.strip()
    
    # Final column selection and ordering
    final_columns = [
        'issn_l', 'title', 'subject', 'impact_proxy', 'apc_usd', 
        'oa_type', 'license', 'waiver', 'cost_efficiency', 
        'last_verified', 'journal_url', 'publisher'
    ]
    
    result_df = base_df[final_columns].copy()
    
    # Remove rows with missing critical data
    result_df = result_df.dropna(subset=['title', 'impact_proxy'])
    
    print(f"Final unified dataset: {len(result_df)} records")
    
    return result_df

def main():
    """Main processing function."""
    print("Journal Choice Transparency - Data Processing")
    print("=" * 50)
    
    # Create unified dataset
    unified_df = create_unified_dataset()
    
    # Save as JSON for frontend
    output_path = Path('public')
    output_path.mkdir(exist_ok=True)
    
    # Convert to JSON format, handling infinity values
    journals_data = unified_df.to_dict('records')

    # Replace infinity values with a large number for JSON compatibility
    for journal in journals_data:
        if journal['cost_efficiency'] == float('inf'):
            journal['cost_efficiency'] = 999999

    with open(output_path / 'journals_mvp.json', 'w') as f:
        json.dump(journals_data, f, indent=2, default=str)
    
    # Also save as CSV for inspection
    unified_df.to_csv(output_path / 'journals_mvp.csv', index=False)
    
    print(f"Saved {len(journals_data)} journals to public/journals_mvp.json")
    print(f"Also saved CSV version to public/journals_mvp.csv")
    
    # Print sample data
    print("\nSample data:")
    print(unified_df.head(3).to_string())

if __name__ == "__main__":
    main()
