#!/usr/bin/env python3
"""
Data processing script for Journal Choice Transparency project.
Processes and merges data from multiple sources into a unified dataset.
"""

import pandas as pd
import numpy as np
import re
from pathlib import Path
import json
from datetime import datetime

def clean_mdpi_data():
    """
    Clean and process MDPI APC data.

    Returns:
        pd.DataFrame: Cleaned MDPI data with standardized columns
    """
    print("Processing MDPI data...")
    df = pd.read_csv('source_data/MDPI_APC.csv')

    # Remove empty rows
    df = df.dropna()

    # Clean column names
    df.columns = ['title', 'apc_chf']

    # Extract numeric APC values and convert CHF to USD (approximate rate: 1 CHF = 1.1 USD)
    # Use NA for missing APC data instead of 0
    apc_values = df['apc_chf'].str.extract(r'(\d+)')
    df['apc_usd'] = pd.to_numeric(apc_values[0], errors='coerce') * 1.1

    # Add metadata
    df['publisher'] = 'MDPI'
    df['oa_type'] = 'Fully OA'
    df['source'] = 'MDPI_APC'

    # Clean title
    df['title'] = df['title'].str.strip()

    return df[['title', 'apc_usd', 'publisher', 'oa_type', 'source']]

def clean_scimago_data():
    """
    Clean and process SciMago Journal Ranking data.
    
    Returns:
        pd.DataFrame: Cleaned SciMago data with impact metrics
    """
    print("Processing SciMago data...")
    df = pd.read_csv('source_data/scimagojr 2024.csv', sep=';')
    
    # Clean column names
    df.columns = [col.strip() for col in df.columns]
    
    # Extract relevant columns
    columns_map = {
        'Title': 'title',
        'Issn': 'issn',
        'SJR': 'sjr',
        'H index': 'h_index',
        'Citations / Doc. (2years)': 'citations_per_doc_2yr',
        'Publisher': 'publisher',
        'Categories': 'categories',
        'Areas': 'areas'
    }
    
    df_clean = df[list(columns_map.keys())].copy()
    df_clean.columns = list(columns_map.values())
    
    # Clean numeric columns
    df_clean['sjr'] = pd.to_numeric(df_clean['sjr'].str.replace(',', '.'), errors='coerce')
    df_clean['citations_per_doc_2yr'] = pd.to_numeric(
        df_clean['citations_per_doc_2yr'].str.replace(',', '.'), errors='coerce'
    )
    
    # Use citations per doc as impact proxy
    df_clean['impact_proxy'] = df_clean['citations_per_doc_2yr']
    
    # Extract primary subject from categories
    df_clean['subject'] = df_clean['categories'].str.split(';').str[0].str.strip()
    df_clean['subject'] = df_clean['subject'].str.replace(r'\s*\([^)]*\)', '', regex=True)
    
    # Clean title
    df_clean['title'] = df_clean['title'].str.strip().str.replace('"', '')
    
    df_clean['source'] = 'SciMago'
    
    return df_clean[['title', 'issn', 'impact_proxy', 'publisher', 'subject', 'source']]

def parse_wiley_excel(filepath, sheet_name=None):
    """
    Parse Wiley Excel files with proper header detection.
    
    Args:
        filepath: Path to Excel file
        sheet_name: Sheet name (optional)
    
    Returns:
        pd.DataFrame: Parsed Wiley data
    """
    print(f"Processing {filepath}...")
    
    try:
        # Read the file and find the header row
        df_raw = pd.read_excel(filepath, sheet_name=sheet_name, header=None)
        
        # Find the row with journal data (look for "Journal" or "ISSN" in first column)
        header_row = None
        for i, row in df_raw.iterrows():
            if any(str(cell).lower().find('journal') != -1 or 
                   str(cell).lower().find('issn') != -1 for cell in row if pd.notna(cell)):
                header_row = i
                break
        
        if header_row is None:
            # Try to find data rows with ISSN pattern
            for i, row in df_raw.iterrows():
                if any(re.match(r'\d{4}-\d{4}', str(cell)) for cell in row if pd.notna(cell)):
                    header_row = max(0, i - 1)
                    break
        
        if header_row is not None:
            # Re-read with proper header
            df = pd.read_excel(filepath, sheet_name=sheet_name, header=header_row)
            # Remove rows before the header
            df = df.iloc[1:].reset_index(drop=True)
        else:
            df = df_raw
        
        return df
    
    except Exception as e:
        print(f"Error parsing {filepath}: {e}")
        return pd.DataFrame()

def process_csv_files():
    """
    Process all CSV files in source_data directory.

    Returns:
        pd.DataFrame: Combined data from all CSV files
    """
    print("Processing CSV files only...")

    csv_files = []
    source_dir = Path('source_data')

    # Find all CSV files
    for csv_file in source_dir.glob('*.csv'):
        if csv_file.name == 'MDPI_APC.csv':
            continue  # Already processed separately

        print(f"Processing {csv_file.name}...")
        try:
            # Try different separators and encodings
            for sep in [',', ';', '\t']:
                try:
                    df = pd.read_csv(csv_file, sep=sep, encoding='utf-8')
                    if len(df.columns) > 1:  # Valid CSV found
                        df['source_file'] = csv_file.name
                        csv_files.append(df)
                        print(f"  Successfully loaded {len(df)} rows from {csv_file.name}")
                        break
                except:
                    continue
        except Exception as e:
            print(f"  Error processing {csv_file.name}: {e}")

    if csv_files:
        return pd.concat(csv_files, ignore_index=True, sort=False)
    else:
        return pd.DataFrame()

def create_unified_dataset():
    """
    Create unified dataset from CSV sources only.

    Returns:
        pd.DataFrame: Unified journal dataset
    """
    print("Creating unified dataset from CSV files only...")

    # Process each data source
    mdpi_df = clean_mdpi_data()
    scimago_df = clean_scimago_data()
    additional_csv_df = process_csv_files()

    print(f"MDPI records: {len(mdpi_df)}")
    print(f"SciMago records: {len(scimago_df)}")
    print(f"Additional CSV records: {len(additional_csv_df)}")

    # Start with SciMago as the base (has impact metrics)
    base_df = scimago_df.copy()

    # Add APC data from MDPI
    mdpi_merge = mdpi_df[['title', 'apc_usd', 'oa_type']].copy()
    base_df = base_df.merge(mdpi_merge, on='title', how='left', suffixes=('', '_mdpi'))

    # Keep APC as NA when not available (don't fill with 0)
    # Only fill oa_type with Unknown when missing
    base_df['oa_type'] = base_df['oa_type'].fillna('Unknown')

    # Calculate cost efficiency - handle NA values properly
    def calculate_cost_efficiency(row):
        if pd.isna(row['apc_usd']) or pd.isna(row['impact_proxy']):
            return np.nan
        elif row['apc_usd'] == 0:
            return float('inf')
        else:
            return row['impact_proxy'] / row['apc_usd']

    base_df['cost_efficiency'] = base_df.apply(calculate_cost_efficiency, axis=1)

    # Add metadata
    base_df['last_verified'] = datetime.now().strftime('%Y-%m-%d')
    base_df['license'] = 'Unknown'
    base_df['waiver'] = False
    base_df['journal_url'] = ''

    # Create ISSN-L (use first ISSN if multiple)
    base_df['issn_l'] = base_df['issn'].str.split(',').str[0].str.strip()

    # Final column selection and ordering
    final_columns = [
        'issn_l', 'title', 'subject', 'impact_proxy', 'apc_usd',
        'oa_type', 'license', 'waiver', 'cost_efficiency',
        'last_verified', 'journal_url', 'publisher'
    ]

    result_df = base_df[final_columns].copy()

    # Remove rows with missing critical data (but keep those with missing APC)
    result_df = result_df.dropna(subset=['title', 'impact_proxy'])

    print(f"Final unified dataset: {len(result_df)} records")
    print(f"Records with APC data: {result_df['apc_usd'].notna().sum()}")
    print(f"Records with missing APC: {result_df['apc_usd'].isna().sum()}")

    return result_df

def main():
    """Main processing function."""
    print("Journal Choice Transparency - Data Processing")
    print("=" * 50)
    
    # Create unified dataset
    unified_df = create_unified_dataset()
    
    # Save as JSON for frontend
    output_path = Path('public')
    output_path.mkdir(exist_ok=True)
    
    # Clean data before JSON conversion
    # Replace NaN values with appropriate defaults, but keep APC as null when missing
    unified_df = unified_df.fillna({
        'title': 'Unknown',
        'subject': 'Unknown',
        'impact_proxy': 0,
        'oa_type': 'Unknown',
        'license': 'Unknown',
        'waiver': False,
        'journal_url': '',
        'publisher': 'Unknown',
        'issn_l': ''
    })
    # Don't fill apc_usd - keep as NaN/null for missing data

    # Convert to JSON format, handling infinity and NaN values
    journals_data = unified_df.to_dict('records')

    # Clean up problematic values for JSON compatibility
    for journal in journals_data:
        # Handle infinity values
        if pd.notna(journal['cost_efficiency']) and journal['cost_efficiency'] == float('inf'):
            journal['cost_efficiency'] = 999999

        # Handle NaN values - convert to null for JSON
        for key, value in journal.items():
            if pd.isna(value):
                if key == 'apc_usd':
                    journal[key] = None  # Keep as null for missing APC data
                elif key in ['impact_proxy', 'cost_efficiency']:
                    journal[key] = 0
                elif key == 'waiver':
                    journal[key] = False
                else:
                    journal[key] = 'Unknown'
            elif isinstance(value, float) and not np.isfinite(value):
                if key in ['impact_proxy', 'cost_efficiency']:
                    journal[key] = 0
                elif key == 'apc_usd':
                    journal[key] = None
                else:
                    journal[key] = 'Unknown'

    with open(output_path / 'journals_mvp.json', 'w') as f:
        json.dump(journals_data, f, indent=2, ensure_ascii=False)
    
    # Also save as CSV for inspection
    unified_df.to_csv(output_path / 'journals_mvp.csv', index=False)
    
    print(f"Saved {len(journals_data)} journals to public/journals_mvp.json")
    print(f"Also saved CSV version to public/journals_mvp.csv")
    
    # Print sample data
    print("\nSample data:")
    print(unified_df.head(3).to_string())

if __name__ == "__main__":
    main()
